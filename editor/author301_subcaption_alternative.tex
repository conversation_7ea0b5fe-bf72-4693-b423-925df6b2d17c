% Alternative version using subcaption package
% This shows how to fix the subfigure issue using modern subcaption syntax

% Add this to the document preamble (before \begin{document}):
% \usepackage{subcaption}

% Then use this figure code:
\begin{figure}[!t]
\centering
\begin{subfigure}{0.48\textwidth}
\centering
\includegraphics[width=\textwidth]{fig301/4a.png}
\caption{Grid search result}
\label{fig:301-4a}
\end{subfigure}%
\hfill%
\begin{subfigure}{0.48\textwidth}
\centering
\includegraphics[width=\textwidth]{fig301/4b.png}
\caption{Confusion matrix}
\label{fig:301-4b}
\end{subfigure}
\caption{Result of the best SVM prediction. (a) Grid search result. (b) Confusion matrix}
\label{fig:301-4}
\end{figure}

% Alternative using legacy subfigure syntax (no additional packages needed):
\begin{figure}[!t]
\centering
\subfigure[Grid search result]{
\label{fig:301-4a}
\includegraphics[width=0.48\textwidth]{fig301/4a.png}}
\hfill
\subfigure[Confusion matrix]{
\label{fig:301-4b}
\includegraphics[width=0.48\textwidth]{fig301/4b.png}}
\caption{Result of the best SVM prediction. (a) Grid search result. (b) Confusion matrix}
\label{fig:301-4}
\end{figure}
