% SUBFIGURE SOLUTIONS FOR SPRINGER SVMULT CLASS
% ===============================================

% SOLUTION 1: MODERN SUBCAPTION PACKAGE (<PERSON><PERSON><PERSON>MENTED IN MAIN FILE - WORKING!)
% This approach uses the subcaption package which is already loaded in editor.tex
% This is the WORKING solution that provides proper side-by-side layout
\begin{figure}[!t]
\centering
\begin{subfigure}{0.48\textwidth}
\centering
\includegraphics[width=\textwidth]{fig301/4a.png}
\caption{Grid search result}
\label{fig:301-4a}
\end{subfigure}%
\hfill%
\begin{subfigure}{0.48\textwidth}
\centering
\includegraphics[width=\textwidth]{fig301/4b.png}
\caption{Confusion matrix}
\label{fig:301-4b}
\end{subfigure}
\caption{Result of the best SVM prediction. (a) Grid search result. (b) Confusion matrix}
\label{fig:301-4}
\end{figure}

% SOLUTION 2: SPRINGER SVMULT CLASS (NOT WORKING PROPERLY)
% This approach was attempted but doesn't provide proper side-by-side layout
% The \leftfigure and \rightfigure commands don't work as expected
\begin{figure}[!t]
\subfigures
\centering
\leftfigure[c]{\includegraphics[width=0.48\textwidth]{fig301/4a.png}}
\leftcaption{Grid search result}
\label{fig:301-4a}
\rightfigure[c]{\includegraphics[width=0.48\textwidth]{fig301/4b.png}}
\rightcaption{Confusion matrix}
\label{fig:301-4b}
\caption{Result of the best SVM prediction. (a) Grid search result. (b) Confusion matrix}
\label{fig:301-4}
\end{figure}

% SOLUTION 2: MODERN SUBCAPTION PACKAGE
% Add this to the document preamble: \usepackage{subcaption}
% NOTE: May conflict with svmult class - use only if subcaption is loaded
\begin{figure}[!t]
\centering
\begin{subfigure}{0.48\textwidth}
\centering
\includegraphics[width=\textwidth]{fig301/4a.png}
\caption{Grid search result}
\label{fig:301-4a}
\end{subfigure}%
\hfill%
\begin{subfigure}{0.48\textwidth}
\centering
\includegraphics[width=\textwidth]{fig301/4b.png}
\caption{Confusion matrix}
\label{fig:301-4b}
\end{subfigure}
\caption{Result of the best SVM prediction. (a) Grid search result. (b) Confusion matrix}
\label{fig:301-4}
\end{figure}

% SOLUTION 3: LEGACY SUBFIGURE SYNTAX
% Works with older subfigure package (no additional packages needed)
\begin{figure}[!t]
\centering
\subfigure[Grid search result]{
\label{fig:301-4a}
\includegraphics[width=0.48\textwidth]{fig301/4a.png}}
\hfill
\subfigure[Confusion matrix]{
\label{fig:301-4b}
\includegraphics[width=0.48\textwidth]{fig301/4b.png}}
\caption{Result of the best SVM prediction. (a) Grid search result. (b) Confusion matrix}
\label{fig:301-4}
\end{figure}

% KEY POINTS:
% - Solution 1 (Springer svmult) is implemented in the main file
% - \subfigures declaration enables alphabetical subnumbering (4a, 4b)
% - \leftfigure[position]{content} and \rightfigure[position]{content} syntax
% - Position options: [l]eft, [c]enter, [r]ight
% - \leftcaption and \rightcaption for individual subfigure captions
% - Main \caption provides overall figure description
