%%%%%%%%%%%%%%%%%%%% author.tex %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%
% sample root file for your "contribution" to a contributed volume
%
% Use this file as a template for your own input.
%
%%%%%%%%%%%%%%%% Springer %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%


%% RECOMMENDED %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%\documentclass[graybox]{svmult}
%
%% choose options for [] as required from the list
%% in the Reference Guide
%
%\usepackage{mathptmx}       % selects Times Roman as basic font
%\usepackage{helvet}         % selects Helvetica as sans-serif font
%\usepackage{courier}        % selects Courier as typewriter font
%\usepackage{type1cm}        % activate if the above 3 fonts are
%                            % not available on your system
%
%\usepackage{makeidx}         % allows index generation
%\usepackage{graphicx}        % standard LaTeX graphics tool
%                            % when including figure files
%\usepackage{multicol}        % used for the two-column index
%\usepackage[bottom]{footmisc}% places footnotes at page bottom
%\usepackage{amsmath}         % For math environments if needed
%\usepackage{amsfonts}        % For math fonts if needed
%\usepackage{amssymb}         % For math symbols if needed
%\usepackage{url}             % For \url command
%
%% see the list of further useful packages
%% in the Reference Guide
%
%\makeindex             % used for the subject index
%                      % please use the style svind.ist with
%                      % your makeindex program
%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%
%\begin{document}

\title{Image-based Skin Disease Classification Using Transfer Learning Model and Fusion Strategy}
% Use \titlerunning{Short Title} for an abbreviated version of
% your contribution title if the original one is too long
\titlerunning{Image-based Skin Disease Classification}
\author{Ya-Ching Yang, Che-Lun Hung, and Yi-Ju Chen}
% Use \authorrunning{Short Title} for an abbreviated version of
% your contribution title if the original one is too long
\authorrunning{Y-C. Yang, C-L. Hung, and Y-J. Chen}
\institute{Ya-Ching Yang \at Institute of Biomedical Informatics, National Yang Ming Chiao Tung University, Taipei, Taiwan, \email{<EMAIL>}
\and Che-Lun Hung \at Institute of Biomedical Informatics, National Yang Ming Chiao Tung University, Taipei, Taiwan, \email{<EMAIL>}
\and Yi-Ju Chen \at Department of Dermatology, Taichung Veterans General Hospital, Taichung, Taiwan, \email{<EMAIL>}}

\maketitle

\abstract{Classifying similar inflammatory skin conditions poses significant challenges for dermatologists. We propose a deep learning approach using transfer learning with pre-trained CNNs to address this issue. By integrating anatomical information through feature fusion, our aim is to enhance diagnostic precision. Among various models evaluated for skin disease classification, MobileNetV3-Large performs excellently with the highest F1-score and minimal computational time. In particular, binary classifications for types of dermatitis, particularly atopic dermatitis and psoriasis lichen planus, proved to be the most challenging. Using feature fusion with anatomical information in a supervised learning setting, SVM emerged as the best performing model, increasing the F1-score from 0.82 to 0.88. This study highlights the importance of integrating limited and anatomical information to improve the diagnosis of complex skin conditions, paving the way for more accurate diagnostic tools for dermatology.}

\keywords{Skin disease classification, Convolutional neural networks, Deep learning model, MobileNetV3-Large, Image classification, Fusion strategy}

\section{Introduction}
\label{sec:301-1}
Inflammatory skin diseases are a prevalent reason for patient visits to dermatology clinics, demonstrating a consistent upward trend \cite{1, 2}. Although not life-threatening, they profoundly impact patients' daily lives. Serious consequences include an increased risk of developing infections, such as cellulitis and impetigo, which can be particularly concerning for those with compromised immune systems. In addition, chronic inflammation can cause scarring and disfigurement, affecting a person's psychological well-being. In some cases, there is also an elevated risk of developing certain types of skin cancer associated with long-standing inflammatory conditions \cite{3}. These diseases, such as acne rosacea (AR), atopic dermatitis (AD), eczema, psoriasis lichen planus (PLP), and fungal infections like tinea ringworm candidiasis (TRC), involve intricate interactions between genetic susceptibility and environmental factors, resulting in overlapping lesions with similar clinical presentations \cite{4, 5}.

Differentiating between similar skin diseases visually can be challenging, especially since severe symptoms can require laboratory tests. In addition, there are no definitive tests for diagnosing AD and its symptoms often overlap with those of other inflammatory skin conditions, complicating clinical assessment. The differentiation of AD from eczema is based heavily on clinical symptoms and medical history, presenting a significant challenge to dermatologists.

A practical approach to addressing the difficulty of diagnosing and distinguishing among inflammatory skin conditions involves the use of advanced technology. Computer vision, when used as an additional tool in dermatology, offers significant potential \cite{6}. Convolutional Neural Networks, in particular, have demonstrated remarkable capabilities that match or exceed the accuracy levels of human physicians \cite{7, 8, 9}. Moreover, transfer learning with these networks can enhance their performance by leveraging knowledge from pre-existing models, making them even more adept at classifying complex skin conditions \cite{10, 11, 12}.

Hence, our research aims to address these challenges by harnessing transfer learning and deep learning techniques to classify inflammatory skin diseases precisely. Incorporating these models into clinical settings could enhance diagnostic precision and patient outcomes by enabling early and accurate identification of specific inflammatory skin conditions. By applying advanced technology, dermatologists can reduce the risks associated with misdiagnosis, particularly among less experienced practitioners, while minimizing subjective interpretations and inconsistencies linked to visual examinations.

\section{Related Work}
\label{sec:301-2}
Recent studies have explored the application of machine learning and deep learning techniques for skin disease detection and classification, showcasing their potential to revolutionize diagnostic processes. Srinivasu \textit{et al.} (2020) introduced a classification approach using MobileNetV2 and Long Short-Term Memory (LSTM) for skin diseases \cite{13}. The MobileNetV2 model has shown effectiveness, displaying improved accuracy on lightweight computational devices. LSTM efficiently handles the issue of gradient disappearance over iterations within neural networks, aiding in faster model training. These findings underscore the efficiency of deep learning models in retaining stateful information for more accurate predictions. The study outperformed other state-of-the-art models, showcasing robustness in recognizing affected regions faster with minimal computational efforts.

In another study, Ahammed \textit{et al.} (2022) proposed a method combining digital hair removal techniques with morphological filtering and machine learning classifiers such as Decision Tree, Support Vector Machine (SVM), and K-Nearest Neighbor (KNN) \cite{14}. Their work emphasizes the importance of automating skin disease prediction to expedite treatment planning, addressing manual diagnoses' time-intensive and subjective nature. It showcases promising potential for improving diagnostic accuracy by integrating multiple techniques.

Muhaba \textit{et al.} (2022) presented an automated system for skin disease diagnosis using a pre-trained MobileNetV2 model, clinical images, and patient information such as age, gender, symptoms, and anatomical location \cite{15}. Their study proposed a smartphone application system designed to serve as a decision support system in low-resource settings, offering excellent diagnostic performance. Integrating patient information with image-based diagnosis demonstrates a holistic approach toward accurate disease identification.

These studies demonstrate diverse approaches researchers have taken, including image segmentation, deep learning neural networks, and patient information integration, to enhance skin disease diagnosis accuracy and efficiency. The utilization of machine learning and deep learning in these studies has shown promising results, providing valuable insights for developing more effective and automated diagnostic tools in dermatology. These advancements underscore the potential of leveraging computational techniques to revolutionize the field of dermatological diagnosis.

\section{Material and Methods}
\label{sec:301-3}

\subsection{Dataset}
\label{subsec:301-3.1}
The image dataset consisted of 967 images of atopic dermatitis, 148 images of eczema, 627 images of acne and rosacea, 1290 images of psoriasis and lichen planus, and 253 images of tinea ringworm candidiasis. Following consultations with dermatologists and considering the limited number of eczema images and their similarities to atopic dermatitis, we merged these two conditions into a single category named atopic dermatitis and eczema. Subsequently, we conducted a preliminary dataset review to remove unsuitable images, including tissue slices, misclassified images, and non-human body parts. Post curation, the dataset comprised 1025 images of atopic dermatitis and eczema, 614 images of acne and rosacea, 975 images of psoriasis and lichen planus, and 246 images of tinea ringworm candidiasis, see Table~\ref{tab:301-1}. This refined dataset forms the foundation for subsequent experiments.

\begin{table}[!t]
\caption{Number of images before and after data set processing}
\label{tab:301-1}
\centering
\begin{tabular}{lcc}
\hline\noalign{\smallskip}
Skin diseases & Original quantity & Quantity after processing \\
\noalign{\smallskip}\hline\noalign{\smallskip}
AR & 627 & 614 \\
AD + Eczema & 1115 & 1025 \\
PLP & 1290 & 975 \\
TRC & 253 & 246 \\
All & 3285 & 2860 \\
\noalign{\smallskip}\hline\noalign{\smallskip}
\end{tabular}
\end{table}

These images exhibit a diverse spectrum of disease manifestations across various body regions. Furthermore, the dataset encompasses two distinct types of photographs:
\begin{itemize}
    \item[a)] Distant view images: These images capture entire body regions affected by skin inflammation, aiding in distinguishing between generalized and localized skin inflammation.
    \item[b)] Close-up images: These images provide a close view of distinct disease features and patterns, making it easier to identify pathological characteristics. However, the exact site of the lesion is not evident in these images.
\end{itemize}
The Dermatology Department at the Taichung Veterans General Hospital in Taiwan generously provided the dataset. We obtained images used in this study from Dermnet, an extensive online dermatology resource. They incorporated Dermnet images and enriched our dataset with a wide array of dermatological conditions, ensuring its suitability for comprehensive research on skin diseases (see Figure~\ref{fig:301-1}).

\subsection{Anatomical Information}
\label{subsec:301-3.2}
In clinical diagnosis, dermatologists rely on factors such as lesion condition, location, patient lifestyle, and medical history \cite{16, 17}. Unfortunately, the dataset we acquired lacks additional relevant information. Discussions with dermatologists emphasized the importance of lesion location in macroscopic diagnoses. Each skin disease tends to manifest in specific areas; for example, acne commonly occurs on the face and nose, while psoriasis is often seen on the knees and elbows, among others \cite{18, 17}. Consequently, we integrated anatomical location information into image names. Collaboration with dermatologists identified crucial anatomical regions for diagnosing various skin conditions (see Table~\ref{tab:301-2}). The dataset categorizes images of different inflammatory skin diseases based on pertinent anatomical regions. Particularly noteworthy is the 'other' category, encompassing images where lesions are either located outside the specified areas of interest or where precise location determination is challenging, thereby classified as 'other.'

\subsection{Data Augmentation}
\label{subsec:301-3.3}
After conducting the preprocessing steps described above, an imbalance in the distribution of images among various skin disease categories became apparent in the dataset. Uneven data distribution in deep learning often results in inadequate model learning and overfitting. To improve the network model's generalization, data augmentation techniques are commonly used to expand the training dataset \cite{8}. We applied data augmentation specifically to categories with fewer instances to counteract the imbalance and mitigate biases favoring classes with larger sample sizes. The goal was to standardize each underrepresented category by augmenting their images to 1025, aligning them with counts in the categories of atopic dermatitis and eczema. We systematically modified the original images in the underrepresented categories employing rotation, flipping, zooming, brightness, and contrast adjustments. This meticulous process ensured a uniform image distribution across all classes, improving dataset consistency. By standardizing image counts, we aim to create a more balanced and robust training environment, reducing the risks associated with biased predictions or overfitting. Ultimately, this augmentation strategy was intended to enhance the accuracy, balance, and consistency of the performance of the model during evaluation.

\begin{figure}[!t]
\centering
\includegraphics[width=0.9\textwidth]{fig301/1.png}
\caption{Four skin disease images. Figures a-d depict Distant View images, while e-h showcase Close-up images. Specifically, a and e represent AR, b and f represent AD\&Ecz, c and g portray PLP, and d and h illustrate TRC}
\label{fig:301-1}
\end{figure}

\begin{table}[!t]
\caption{Key anatomical locations for each skin disease. The key anatomical locations focused on each skin disease can be organized as follows. 'Other' denotes that if the skin disease image does not pertain to the main anatomical locations affected by the disease, it will be categorized as 'Other'}
\label{tab:301-2}
\centering
\begin{tabular}{p{4.5cm}p{7cm}}
\hline\noalign{\smallskip}
Skin disease & Key anatomical locations \\
\noalign{\smallskip}\hline\noalign{\smallskip}
Acne and Rosacea & face, nose, other \\
Atopic Dermatitis and Eczema & scalp, eyebrow, eyelid, ear, elbow and knee creases, palms and soles, limbs, trunk, other \\
Psoriasis and Lichen Planus & scalp, fingers, elbow, knee, toes, nails, trunk, other \\
Tinea Ringworm Candidiasis & quarrel side, toe gap, armpit, crotch, trunk, other \\
\noalign{\smallskip}\hline\noalign{\smallskip}
\end{tabular}
\end{table}

\subsection{Experiment Design}
\label{subsec:301-3.4}
Our study used transfer learning using publicly available pre-trained models on Kaggle and Tensorflow. Specifically, we selected models commonly used in skin disease image research, such as MobileNet, ResNet, VGGNet, DenseNet, and Xception \cite{6, 19}, pre-trained on the ImageNet dataset for general image classification tasks. Transfer learning enabled these pre-trained models to leverage the knowledge from ImageNet to improve performance in classifying the inflammatory skin disease image. We selected models by training and predicting on inflammatory skin disease images from our curated skin disease dataset to compare their performance. Our selection criteria focused on choosing lightweight models with high performance and low time consumption to identify the best model for skin disease classification. We used the selected model with the best performance in subsequent experiments.

We maintained consistent settings for each model. Our model's configuration included specific parameters: the use of the Adam optimizer, categorical cross-entropy loss function, a learning rate of 0.0002, and a batch size of 32. The dataset was partitioned, with 80\% allocated for training, 10\% reserved for validation, and 10\% used for testing the model's performance.

In subsequent experiments, we identified the significance of incorporating anatomical information in the diagnosis of inflammatory skin diseases. After fine-tuning the pre-trained model with the best performance for inflammatory skin disease image classification, we fused the predicted image probabilities with anatomical information and used the image labels as ground truth to measure the model's predictive performance. This fusion approach was then input into a supervised learning model for final prediction, with the aim of enhancing the supervised learning model's decision-making capabilities. Standard supervised learning models, such as SVM, Random Forest, Decision Tree, XGBoost, and CatBoost, were evaluated to determine the most suitable model for the classification task of inflammatory skin disease images; the benefit of employing a supervised learning model as the final predictive tool is its ability to learn from labeled data, allowing it to generalize effectively to new, unseen data. This approach is particularly advantageous in dermatology, where labeled datasets are scarce but invaluable. Moreover, supervised learning models are easily interpretable, allowing clinicians to understand the reasoning behind model predictions, thus increasing trust in the model's recommendations (see Figure~\ref{fig:301-2}).

\begin{figure}[!t]
\centering
\includegraphics[width=0.9\textwidth]{fig301/2.png}
\caption{Experiment workflow}
\label{fig:301-2}
\end{figure}

\section{Result}
\label{sec:301-4}

\subsection{Classification Model Comparison}
\label{subsec:301-4.1}
In comparing commonly used models for skin image classification, the MobileNet series emerged as particularly suitable for our task, with results indicating an overall performance exceeding 0.7 for crucial metrics in Table~\ref{tab:301-3}. Specifically, MobileNetv3-Large demonstrated the best performance, achieving a test accuracy, precision, recall, and F1-score of 0.82 on 416 test images. Although MobileNetv3-Large does not have the lowest time consumption among the models considered, it still required less time than most models (Table~\ref{tab:301-3}). Consequently, this factor did not deter us from selecting MobileNetv3-Large for subsequent experiments.

\begin{figure}[!t]
\centering
\includegraphics[width=\textwidth]{fig301/3.png}
\caption{Fine-tune MobileNetV3-L classification result. (a) Accuracy curve. (b) Confusion matrix}
\label{fig:301-3}
\end{figure}

\begin{table}[!t]
\caption{The comparison of commonly used models for skin image classification}
\label{tab:301-3}
\centering
\begin{tabular}{lcccccc}
\hline\noalign{\smallskip}
Model & Accuracy & Precision & Recall & F1-score & Train(s) & Inference(s) \\
\noalign{\smallskip}\hline\noalign{\smallskip}
MobileNetV3-L & 0.82 & 0.83 & 0.82 & 0.82 & 120.67 & 1.24 \\
MobileNetV3-S & 0.78 & 0.79 & 0.78 & 0.78 & 108.13 & 1.01 \\
Nasnet-mobile & 0.76 & 0.77 & 0.76 & 0.76 & 238.83 & 2.79 \\
MobileNetV2 & 0.71 & 0.73 & 0.71 & 0.7 & 115.37 & 1.16 \\
Inception-v3 & 0.67 & 0.71 & 0.67 & 0.67 & 135.73 & 1.51 \\
DenseNet201 & 0.59 & 0.65 & 0.59 & 0.6 & 409.86 & 3.65 \\
ResNetV2 152 & 0.6 & 0.68 & 0.6 & 0.59 & 299.51 & 2.31 \\
VGG19 & 0.47 & 0.53 & 0.47 & 0.45 & 296.7 & 1.47 \\
Xception & 0.25 & 0.06 & 0.25 & 0.1 & 264.04 & 1.78 \\
\noalign{\smallskip}\hline\noalign{\smallskip}
\end{tabular}
\end{table}

\subsection{Multi-class Classification}
\label{subsec:301-4.2}
After fine-tuning MobileNetv3-Large using clinical images of inflammatory skin disease, the model achieved a training accuracy of 0.95 and a validation accuracy of 0.83 in the 76th epoch (see Figure~\ref{fig:301-3}(a)). The confusion matrix revealed that the model successfully predicted 336 out of 410 verification images, resulting in a testing accuracy of 0.81 (see Figure~\ref{fig:301-3}(b)). Analysis of the confusion matrix indicated that the model performed well in classifying AR and TRC. However, it faced challenges distinguishing between AD and PLP, often confusing the two conditions.

\subsection{Results of Supervised Learning Model with Fused Features}
\label{subsec:301-4.3}
Train five standard supervised learning models using fused features. The parameters of the supervised learning models are default. Each supervised learning model demonstrated exemplary performance. SVM achieved the best results as the final model for predicting inflammatory skin diseases, with an accuracy of 0.87, precision of 0.88, recall of 0.87, and F1-score of 0.87 (Table~\ref{tab:301-4}). Using GridSearchCV from sklearn, we identified the SVM's best parameters (C, gamma, and kernel). The optimal parameter settings were "C" set to 100, "gamma" set to "auto," and "kernel" set to "rbf." (see Figure~\ref{fig:301-4}(a)) The accuracy, precision, recall, and F1-score of the best SVM classification performance were 0.88 (see Figure~\ref{fig:301-4}(b)).

\begin{figure}[!t]
\subfigures
\centering
\leftfigure[c]{\includegraphics[width=0.48\textwidth]{fig301/4a.png}}
\leftcaption{Grid search result}
\label{fig:301-4a}
\rightfigure[c]{\includegraphics[width=0.48\textwidth]{fig301/4b.png}}
\rightcaption{Confusion matrix}
\label{fig:301-4b}
\caption{Result of the best SVM prediction. (a) Grid search result. (b) Confusion matrix}
\label{fig:301-4}
\end{figure}

Table~\ref{tab:301-5} shows that the incorporation of anatomical information into the classification of inflammatory skin diseases significantly improves the predictions of the model.

\begin{table}[!t]
\caption{Classification results of default parameter models}
\label{tab:301-4}
\centering
\begin{tabular}{lcccc}
\hline\noalign{\smallskip}
Model & Accuracy & Precision & Recall & F1-score \\
\noalign{\smallskip}\hline\noalign{\smallskip}
SVM & 0.87 & 0.88 & 0.87 & 0.87 \\
XGBoost & 0.86 & 0.86 & 0.86 & 0.86 \\
Decision Tree & 0.83 & 0.85 & 0.83 & 0.84 \\
CatBoost & 0.83 & 0.84 & 0.83 & 0.83 \\
Random Forest & 0.82 & 0.82 & 0.82 & 0.82 \\
\noalign{\smallskip}\hline\noalign{\smallskip}
\end{tabular}
\end{table}

\section{Discussion}
\label{sec:301-5}
This study aimed to help dermatologists diagnose common, similar inflammatory skin diseases by classifying four inflammatory skin diseases using a transfer learning model and fusion strategy. MobileNet-Large was identified as the most suitable model for classifying the image dataset. We used a fusion strategy to append anatomical information to the best-supervised learning model, SVM, for the final prediction.

The four inflammatory skin diseases were paired to simplify the classification task. The challenges in classifying AD and PLP were revealed in the fine-tuned MobileNet-Large model's classification results. The aim was to identify pairs of challenging diseases for the model to learn. Table~\ref{tab:301-6} shows the classification accuracy between each pair of skin diseases using the fine-tuned MobileNetV3-Large model. Most disease pairs, with an overall indicator greater than 0.8, except AD / PLP. The results of the AD / PLP classification showed an accuracy of 0.76, a precision of 0.78, a recall of 0.76 and an F1-score of 0.74, highlighting the difficulty in accurately classifying AD and PLP, a crucial aspect of our research. To address this challenge, we fuse the features output by the model with the information of the body parts. We input them into the supervised learning model for the final prediction, improving the classification accuracy of AD/PLP and enhancing the model's overall performance.

\begin{table}[!t]
\caption{Performance comparison with and without anatomical info}
\label{tab:301-5}
\centering
\begin{tabular}{lcccc}
\hline\noalign{\smallskip}
Architecture & Accuracy & Precision & Recall & F1-score \\
\noalign{\smallskip}\hline\noalign{\smallskip}
Fine-tune MobileNetV3-L(no anatomical info.) & 0.81 & 0.81 & 0.80 & 0.81 \\
Fine-tune SVM (fusing anatomical info.) & 0.88 & 0.88 & 0.88 & 0.88 \\
\noalign{\smallskip}\hline\noalign{\smallskip}
\end{tabular}
\end{table}

\begin{table}[!t]
\caption{Result of binary classification}
\label{tab:301-6}
\centering
\begin{tabular}{lcccc}
\hline\noalign{\smallskip}
Image pair & Accuracy & Precision & Recall & F1-score \\
\noalign{\smallskip}\hline\noalign{\smallskip}
AD/PLP & 0.76 & 0.78 & 0.76 & 0.74 \\
AR/TRC & 0.86 & 0.89 & 0.86 & 0.86 \\
PLP/TRC & 0.87 & 0.89 & 0.87 & 0.87 \\
AR/AD & 0.9 & 0.9 & 0.9 & 0.9 \\
AD/TRC & 0.92 & 0.92 & 0.92 & 0.92 \\
AR/PLP & 0.93 & 0.93 & 0.93 & 0.93 \\
\noalign{\smallskip}\hline\noalign{\smallskip}
\end{tabular}
\end{table}

In clinical practice, dermatologists cannot distinguish two skin diseases very well, as the signs between the diseases are very similar. They typically rely on a combination of patient information, including anatomy, disease history, and test results, to make accurate diagnoses \cite{20}. Incorporating anatomical information into the model's features significantly improved performance for most disease pairs, with F1-scores reaching up to 0.95 and achieving 1 for AR/AD (see Table~\ref{tab:301-7}). However, the classification results for AD/PLP remained suboptimal, with an F1-score of only 0.75, reflecting the challenges dermatologists face clinically with these two types of disease.

\begin{table}[!t]
\caption{Final results of binary classification}
\label{tab:301-7}
\centering
\begin{tabular}{lcccc}
\hline\noalign{\smallskip}
Image pair & Accuracy & Precision & Recall & F1-score \\
\noalign{\smallskip}\hline\noalign{\smallskip}
AD/PLP & 0.77 & 0.84 & 0.77 & 0.75 \\
AR/TRC & 0.82 & 0.84 & 0.82 & 0.82 \\
AD/TRC & 0.92 & 0.92 & 0.92 & 0.92 \\
AR/PLP & 0.97 & 0.98 & 0.97 & 0.97 \\
PLP/TRC & 0.97 & 0.98 & 0.97 & 0.97 \\
AR/AD & 1.0 & 1.0 & 1.0 & 1.0 \\
\noalign{\smallskip}\hline\noalign{\smallskip}
\end{tabular}
\end{table}

Our study focused on the use of transfer learning models and fusion strategies to address the complex diagnosis of inflammatory skin diseases with limited imaging data. When classifying using only images, our results achieve similar accuracy to those of Muhaba \textit{et al.}, although we do not classify all the same inflammatory skin diseases.

In clinical practice, nonspecialist doctors often lack extensive information about patients with dermatitis and can only observe the characteristics of lesions to make a diagnosis. Therefore, our study is more suitable for general clinical use, such as in nondermatology clinics and hospitals, where access to specialized dermatological expertise may be limited. Our approach could improve diagnostic accuracy and streamline decision-making processes in these settings, ultimately improving patient care.

To enhance future research, we plan to collaborate with medical professionals to incorporate domain knowledge into the model, such as assigning weights to crucial body parts. This approach will enable the model to learn the importance of different parts of the body in the diagnosis of skin disease. In addition, we intend to acquire a wider range of information types to enrich our dataset. Furthermore, to address the issue of data imbalance, our aim is to source image resources for other inflammatory skin diseases to expand our database.

\section{Conclusion}
\label{sec:301-6}
This study used deep learning methodologies to classify inflammatory skin diseases, using a curated dataset and the best CNN model, such as MobileNetV3-Large, as the primary model. Our results demonstrate significant progress in the classification of skin diseases, particularly in the integration of anatomical information to enhance the precision of classification.

Despite this progress, accurately classifying AD and PLP remains challenging, highlighting the need for further refinement. Integrating these technologies can revolutionize diagnostic processes, especially telemedicine, by improving initial assessments. Despite its limitations, this methodology provides a basis for accurately distinguishing between complex skin diseases.

\begin{acknowledgement}
This study is financially supported by the Taiwan National Science and Technology Council (NSTC) under the NSTC grants 112-2314-B-A49-049-MY3 and 112-2634-F-A49-003. Additionally, we acknowledge the use of OpenAI's ChatGPT for grammar and language improvements.
\end{acknowledgement}


\input{references301}
%\end{document}
