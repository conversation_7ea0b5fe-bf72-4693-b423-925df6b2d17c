\relax 
\@writefile{toc}{\contentsline {chapter}{Glossary}{37}{}\protected@file@percent }
\@writefile{toc}{\contentsline {paragraph}{glossary term}{37}{}\protected@file@percent }
\@writefile{toc}{\contentsline {paragraph}{glossary term}{37}{}\protected@file@percent }
\@writefile{toc}{\contentsline {paragraph}{glossary term}{37}{}\protected@file@percent }
\@writefile{toc}{\contentsline {paragraph}{glossary term}{37}{}\protected@file@percent }
\@writefile{toc}{\contentsline {paragraph}{glossary term}{37}{}\protected@file@percent }
\@setckpt{glossary}{
\setcounter{page}{38}
\setcounter{equation}{1}
\setcounter{enumi}{0}
\setcounter{enumii}{0}
\setcounter{enumiii}{0}
\setcounter{enumiv}{22}
\setcounter{footnote}{0}
\setcounter{mpfootnote}{0}
\setcounter{part}{1}
\setcounter{section}{1}
\setcounter{subsection}{1}
\setcounter{subsubsection}{1}
\setcounter{paragraph}{0}
\setcounter{subparagraph}{0}
\setcounter{figure}{1}
\setcounter{table}{1}
\setcounter{minitocdepth}{0}
\setcounter{chapter}{1}
\setcounter{theorem}{0}
\setcounter{case}{0}
\setcounter{conjecture}{0}
\setcounter{corollary}{0}
\setcounter{definition}{0}
\setcounter{example}{0}
\setcounter{exercise}{0}
\setcounter{lemma}{0}
\setcounter{note}{0}
\setcounter{problem}{0}
\setcounter{property}{0}
\setcounter{proposition}{0}
\setcounter{question}{0}
\setcounter{solution}{0}
\setcounter{remark}{0}
\setcounter{prob}{0}
\setcounter{merk}{0}
\setcounter{@inst}{1}
\setcounter{@auth}{1}
\setcounter{auco}{1}
\setcounter{contribution}{2}
\setcounter{parentequation}{0}
\setcounter{AM@survey}{0}
\setcounter{caption@flags}{2}
\setcounter{continuedfloat}{0}
\setcounter{subfigure}{0}
\setcounter{subtable}{0}
\setcounter{@pps}{0}
\setcounter{@ppsavesec}{0}
\setcounter{@ppsaveapp}{0}
\setcounter{float@type}{16}
\setcounter{algorithm}{0}
\setcounter{ALG@line}{0}
\setcounter{ALG@rem}{0}
\setcounter{ALG@nested}{0}
\setcounter{ALG@Lnr}{2}
\setcounter{ALG@blocknr}{10}
\setcounter{ALG@storecount}{0}
\setcounter{ALG@tmpcounter}{0}
\setcounter{lstnumber}{1}
\setcounter{lstlisting}{0}
}
